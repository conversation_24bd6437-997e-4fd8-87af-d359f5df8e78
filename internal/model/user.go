package model

import (
	"time"
)

// User 用户模型
type User struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	Username  string    `gorm:"column:username;uniqueIndex" json:"username"`
	Password  string    `gorm:"column:password" json:"-"`
	RealName  string    `gorm:"column:real_name" json:"realName"`
	Phone     string    `gorm:"column:phone;uniqueIndex" json:"phone"`
	Email     string    `gorm:"column:email" json:"email"`
	UserType  int8      `gorm:"column:user_type" json:"userType"` // 1-拍卖师 2-买家 3-管理员 4-质检员
	Status    int8      `gorm:"column:status" json:"status"`      // 0-禁用 1-启用
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// Role 角色模型
type Role struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name" json:"name"`
	Code        string    `gorm:"column:code;uniqueIndex" json:"code"`
	Description string    `gorm:"column:description" json:"description"`
	Status      int8      `gorm:"column:status" json:"status"` // 0-禁用 1-启用
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "role"
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID    int64     `gorm:"column:user_id;uniqueIndex:uk_user_role" json:"userId"`
	RoleID    int64     `gorm:"column:role_id;uniqueIndex:uk_user_role" json:"roleId"`
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_role"
}

// UserWithRoles 用户信息（包含角色）
type UserWithRoles struct {
	User
	Roles []Role `json:"roles"`
}
