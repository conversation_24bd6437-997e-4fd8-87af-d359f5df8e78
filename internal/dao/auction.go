package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// AuctionDAO 拍卖数据访问接口
type AuctionDAO interface {
	CreateAuction(ctx context.Context, auction *model.Auction) error
	UpdateAuction(ctx context.Context, auction *model.Auction) error
	FindAuctionByID(ctx context.Context, id int64) (*model.Auction, error)
	ListAuctions(ctx context.Context, offset, limit int) ([]*model.Auction, error)
	CountAuctions(ctx context.Context) (int64, error)

	CreateAuctionItem(ctx context.Context, item *model.AuctionItem) error
	UpdateAuctionItem(ctx context.Context, item *model.AuctionItem) error
	FindAuctionItemByID(ctx context.Context, id int64) (*model.AuctionItem, error)
	ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItem, error)

	CreateBid(ctx context.Context, bid *model.Bid) error
	ListBidsByItem(ctx context.Context, itemID int64) ([]*model.Bid, error)
	GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error)
	CountBidsByItem(ctx context.Context, itemID int64) (int64, error)
}

// auctionDAO 拍卖数据访问实现
type auctionDAO struct {
	db *gorm.DB
}

// NewAuctionDAO 创建拍卖数据访问实例
func NewAuctionDAO() AuctionDAO {
	return &auctionDAO{
		db: GetAuctionDB(),
	}
}

// CreateAuction 创建拍卖会
func (d *auctionDAO) CreateAuction(ctx context.Context, auction *model.Auction) error {
	return d.db.WithContext(ctx).Create(auction).Error
}

// UpdateAuction 更新拍卖会
func (d *auctionDAO) UpdateAuction(ctx context.Context, auction *model.Auction) error {
	return d.db.WithContext(ctx).Save(auction).Error
}

// FindAuctionByID 根据ID查找拍卖会
func (d *auctionDAO) FindAuctionByID(ctx context.Context, id int64) (*model.Auction, error) {
	var auction model.Auction
	if err := d.db.WithContext(ctx).First(&auction, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &auction, nil
}

// ListAuctions 查询拍卖会列表
func (d *auctionDAO) ListAuctions(ctx context.Context, offset, limit int) ([]*model.Auction, error) {
	var auctions []*model.Auction
	if err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&auctions).Error; err != nil {
		return nil, err
	}
	return auctions, nil
}

// CountAuctions 统计拍卖会总数
func (d *auctionDAO) CountAuctions(ctx context.Context) (int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&model.Auction{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CreateAuctionItem 创建拍卖商品
func (d *auctionDAO) CreateAuctionItem(ctx context.Context, item *model.AuctionItem) error {
	return d.db.WithContext(ctx).Create(item).Error
}

// UpdateAuctionItem 更新拍卖商品
func (d *auctionDAO) UpdateAuctionItem(ctx context.Context, item *model.AuctionItem) error {
	return d.db.WithContext(ctx).Save(item).Error
}

// FindAuctionItemByID 根据ID查找拍卖商品
func (d *auctionDAO) FindAuctionItemByID(ctx context.Context, id int64) (*model.AuctionItem, error) {
	var item model.AuctionItem
	if err := d.db.WithContext(ctx).First(&item, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

// ListAuctionItems 查询拍卖商品列表
func (d *auctionDAO) ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItem, error) {
	var items []*model.AuctionItem
	if err := d.db.WithContext(ctx).Where("auction_id = ?", auctionID).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

// CreateBid 创建竞价记录
func (d *auctionDAO) CreateBid(ctx context.Context, bid *model.Bid) error {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	return d.db.WithContext(ctx).Table(tableName).Create(bid).Error
}

// ListBidsByItem 查询商品的竞价记录
func (d *auctionDAO) ListBidsByItem(ctx context.Context, itemID int64) ([]*model.Bid, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	var bids []*model.Bid
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("auction_item_id = ?", itemID).
		Order("price DESC").
		Find(&bids).Error; err != nil {
		return nil, err
	}
	return bids, nil
}

// GetHighestBid 获取商品的最高出价
func (d *auctionDAO) GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	var bid model.Bid
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("auction_item_id = ? AND status = 1", itemID).
		Order("price DESC").
		First(&bid).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &bid, nil
}

// CountBidsByItem 统计商品的竞价次数
func (d *auctionDAO) CountBidsByItem(ctx context.Context, itemID int64) (int64, error) {
	// 获取当前月份的表名
	tableName := fmt.Sprintf("bid_%s", time.Now().Format("200601"))
	var count int64
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("auction_item_id = ?", itemID).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
