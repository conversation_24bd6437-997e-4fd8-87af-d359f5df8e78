package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/service"
)

// AuctionHandler 拍卖相关接口处理器
type AuctionHandler struct {
	auctionService service.AuctionService
}

// NewAuctionHandler 创建拍卖接口处理器
func NewAuctionHandler() *AuctionHandler {
	return &AuctionHandler{
		auctionService: service.NewAuctionService(),
	}
}

// RegisterRoutes 注册路由
func (h *AuctionHandler) RegisterRoutes(r *gin.Engine) {
	// 拍卖会管理
	auction := r.Group("/api/v1/auctions")
	{
		auction.POST("", h.CreateAuction)
		auction.PUT("/:id", h.UpdateAuction)
		auction.GET("/:id", h.GetAuction)
		auction.GET("", h.ListAuctions)
		auction.PUT("/:id/status", h.UpdateAuctionStatus)
	}

	// 拍卖商品管理
	item := r.Group("/api/v1/auction-items")
	{
		item.POST("", h.AddAuctionItem)
		item.PUT("/:id", h.UpdateAuctionItem)
		item.GET("/:id", h.GetAuctionItem)
		item.GET("/auction/:auctionId", h.ListAuctionItems)
		item.PUT("/:id/status", h.UpdateAuctionItemStatus)
	}

	// 竞价管理
	bid := r.Group("/api/v1/bids")
	{
		bid.POST("", h.PlaceBid)
		bid.GET("/item/:itemId", h.ListBids)
		bid.GET("/item/:itemId/highest", h.GetHighestBid)
	}
}

// CreateAuctionRequest 创建拍卖会请求
type CreateAuctionRequest struct {
	Name         string    `json:"name" binding:"required"`
	StartTime    time.Time `json:"startTime" binding:"required"`
	EndTime      time.Time `json:"endTime" binding:"required"`
	AuctioneerID int64     `json:"auctioneerId" binding:"required"`
	Description  string    `json:"description"`
}

// CreateAuction 创建拍卖会
func (h *AuctionHandler) CreateAuction(c *gin.Context) {
	var req CreateAuctionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	auction, err := h.auctionService.CreateAuction(c.Request.Context(), req.Name, req.StartTime, req.EndTime, req.AuctioneerID, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, auction)
}

// UpdateAuctionRequest 更新拍卖会请求
type UpdateAuctionRequest struct {
	Name        string    `json:"name" binding:"required"`
	StartTime   time.Time `json:"startTime" binding:"required"`
	EndTime     time.Time `json:"endTime" binding:"required"`
	Description string    `json:"description"`
}

// UpdateAuction 更新拍卖会
func (h *AuctionHandler) UpdateAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuction(c.Request.Context(), auctionID, req.Name, req.StartTime, req.EndTime, req.Description); err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖会更新成功"})
}

// GetAuction 获取拍卖会信息
func (h *AuctionHandler) GetAuction(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	auction, err := h.auctionService.GetAuction(c.Request.Context(), auctionID)
	if err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, auction)
}

// ListAuctions 查询拍卖会列表
func (h *AuctionHandler) ListAuctions(c *gin.Context) {
	page, size := ParsePagination(c)
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))

	auctions, total, err := h.auctionService.ListAuctions(c.Request.Context(), int8(status), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  auctions,
		Total: total,
		Page:  page,
		Size:  size,
	})
}

// UpdateAuctionStatusRequest 更新拍卖会状态请求
type UpdateAuctionStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1 2"` // 0-未开始 1-进行中 2-已结束
}

// UpdateAuctionStatus 更新拍卖会状态
func (h *AuctionHandler) UpdateAuctionStatus(c *gin.Context) {
	auctionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuctionStatus(c.Request.Context(), auctionID, req.Status); err != nil {
		if err == service.ErrAuctionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖会不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖会状态更新成功"})
}

// AddAuctionItemRequest 添加拍卖商品请求
type AddAuctionItemRequest struct {
	AuctionID  int64     `json:"auctionId" binding:"required"`
	ProductID  int64     `json:"productId" binding:"required"`
	StartPrice float64   `json:"startPrice" binding:"required,min=0"`
	StepPrice  float64   `json:"stepPrice" binding:"required,min=0"`
	StartTime  time.Time `json:"startTime" binding:"required"`
}

// AddAuctionItem 添加拍卖商品
func (h *AuctionHandler) AddAuctionItem(c *gin.Context) {
	var req AddAuctionItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.auctionService.AddAuctionItem(c.Request.Context(), req.AuctionID, req.ProductID, req.StartPrice, req.StepPrice, req.StartTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// UpdateAuctionItemRequest 更新拍卖商品请求
type UpdateAuctionItemRequest struct {
	StartPrice float64   `json:"startPrice" binding:"required,min=0"`
	StepPrice  float64   `json:"stepPrice" binding:"required,min=0"`
	StartTime  time.Time `json:"startTime" binding:"required"`
}

// UpdateAuctionItem 更新拍卖商品
func (h *AuctionHandler) UpdateAuctionItem(c *gin.Context) {
	itemID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuctionItem(c.Request.Context(), itemID, req.StartPrice, req.StepPrice, req.StartTime); err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖商品更新成功"})
}

// GetAuctionItem 获取拍卖商品信息
func (h *AuctionHandler) GetAuctionItem(c *gin.Context) {
	itemID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	item, err := h.auctionService.GetAuctionItem(c.Request.Context(), itemID)
	if err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

// ListAuctionItems 查询拍卖商品列表
func (h *AuctionHandler) ListAuctionItems(c *gin.Context) {
	auctionID, err := ParseParamID(c, "auctionId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	items, err := h.auctionService.ListAuctionItems(c.Request.Context(), auctionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, items)
}

// UpdateAuctionItemStatusRequest 更新拍卖商品状态请求
type UpdateAuctionItemStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1 2 3"` // 0-未开始 1-竞拍中 2-已成交 3-流拍
}

// UpdateAuctionItemStatus 更新拍卖商品状态
func (h *AuctionHandler) UpdateAuctionItemStatus(c *gin.Context) {
	itemID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateAuctionItemStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.UpdateAuctionItemStatus(c.Request.Context(), itemID, req.Status); err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "拍卖商品状态更新成功"})
}

// PlaceBidRequest 竞价请求
type PlaceBidRequest struct {
	ItemID int64   `json:"itemId" binding:"required"`
	UserID int64   `json:"userId" binding:"required"`
	Price  float64 `json:"price" binding:"required,min=0"`
}

// PlaceBid 竞价
func (h *AuctionHandler) PlaceBid(c *gin.Context) {
	var req PlaceBidRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.auctionService.PlaceBid(c.Request.Context(), req.ItemID, req.UserID, req.Price); err != nil {
		if err == service.ErrAuctionItemNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "拍卖商品不存在"})
			return
		}
		if err == service.ErrInvalidBidPrice {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "出价无效"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "竞价成功"})
}

// ListBids 查询竞价记录
func (h *AuctionHandler) ListBids(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	bids, err := h.auctionService.ListBids(c.Request.Context(), itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, bids)
}

// GetHighestBid 获取最高出价
func (h *AuctionHandler) GetHighestBid(c *gin.Context) {
	itemID, err := ParseParamID(c, "itemId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	bid, err := h.auctionService.GetHighestBid(c.Request.Context(), itemID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, bid)
}
