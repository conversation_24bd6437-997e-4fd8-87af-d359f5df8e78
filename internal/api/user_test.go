package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockUserService 模拟用户服务
type MockUserService struct {
	mock.Mock
}

func (m *MockUserService) Register(ctx context.Context, username, password, realName, phone, email string, userType int8) (*model.User, error) {
	args := m.Called(ctx, username, password, realName, phone, email, userType)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserService) Login(ctx context.Context, username, password string) (*model.UserWithRoles, error) {
	args := m.Called(ctx, username, password)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserWithRoles), args.Error(1)
}

func (m *MockUserService) ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error {
	args := m.Called(ctx, userID, oldPassword, newPassword)
	return args.Error(0)
}

func (m *MockUserService) UpdateProfile(ctx context.Context, userID int64, realName, phone, email string) error {
	args := m.Called(ctx, userID, realName, phone, email)
	return args.Error(0)
}

func (m *MockUserService) GetUserInfo(ctx context.Context, userID int64) (*model.UserWithRoles, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserWithRoles), args.Error(1)
}

func (m *MockUserService) UpdateUserStatus(ctx context.Context, userID int64, status int8) error {
	args := m.Called(ctx, userID, status)
	return args.Error(0)
}

func (m *MockUserService) ListUsers(ctx context.Context, page, size int) ([]*model.User, int64, error) {
	args := m.Called(ctx, page, size)
	return args.Get(0).([]*model.User), args.Get(1).(int64), args.Error(2)
}

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	return r
}

func TestUserHandler_Register(t *testing.T) {
	mockService := new(MockUserService)
	handler := &UserHandler{userService: mockService}

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功注册",
			requestBody: RegisterRequest{
				Username: "testuser",
				Password: "password123",
				RealName: "测试用户",
				Phone:    "13800138000",
				Email:    "<EMAIL>",
				UserType: 1,
			},
			setup: func() {
				user := &model.User{
					ID:       1,
					Username: "testuser",
					RealName: "测试用户",
					Phone:    "13800138000",
					Email:    "<EMAIL>",
					UserType: 1,
					Status:   1,
				}
				mockService.On("Register", mock.Anything, "testuser", "password123", "测试用户", "13800138000", "<EMAIL>", int8(1)).Return(user, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `"username":"testuser"`,
		},
		{
			name: "请求参数错误",
			requestBody: RegisterRequest{
				Username: "", // 空用户名
				Password: "password123",
				RealName: "测试用户",
				Phone:    "13800138000",
				Email:    "<EMAIL>",
				UserType: 1,
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"error"`,
		},
		{
			name: "无效的JSON",
			requestBody: `{"invalid": json}`,
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"error"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := setupTestRouter()
			r.POST("/register", handler.Register)

			// 准备请求体
			var body []byte
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, _ = json.Marshal(tt.requestBody)
			}

			// 创建请求
			req, _ := http.NewRequest("POST", "/register", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestUserHandler_Login(t *testing.T) {
	mockService := new(MockUserService)
	handler := &UserHandler{userService: mockService}

	tests := []struct {
		name           string
		requestBody    LoginRequest
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功登录",
			requestBody: LoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			setup: func() {
				userWithRoles := &model.UserWithRoles{
					User: model.User{
						ID:       1,
						Username: "testuser",
						RealName: "测试用户",
						Status:   1,
					},
					Roles: []model.Role{},
				}
				mockService.On("Login", mock.Anything, "testuser", "password123").Return(userWithRoles, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `"username":"testuser"`,
		},
		{
			name: "请求参数错误",
			requestBody: LoginRequest{
				Username: "", // 空用户名
				Password: "password123",
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"error"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := setupTestRouter()
			r.POST("/login", handler.Login)

			// 准备请求体
			body, _ := json.Marshal(tt.requestBody)

			// 创建请求
			req, _ := http.NewRequest("POST", "/login", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestUserHandler_GetUserInfo(t *testing.T) {
	mockService := new(MockUserService)
	handler := &UserHandler{userService: mockService}

	tests := []struct {
		name           string
		userID         string
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name:   "成功获取用户信息",
			userID: "1",
			setup: func() {
				userWithRoles := &model.UserWithRoles{
					User: model.User{
						ID:       1,
						Username: "testuser",
						RealName: "测试用户",
						Status:   1,
					},
					Roles: []model.Role{},
				}
				mockService.On("GetUserInfo", mock.Anything, int64(1)).Return(userWithRoles, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `"username":"testuser"`,
		},
		{
			name:           "无效的用户ID",
			userID:         "invalid",
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   `"error"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := setupTestRouter()
			r.GET("/users/:id", handler.GetUserInfo)

			// 创建请求
			req, _ := http.NewRequest("GET", "/users/"+tt.userID, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestUserHandler_ListUsers(t *testing.T) {
	mockService := new(MockUserService)
	handler := &UserHandler{userService: mockService}

	tests := []struct {
		name           string
		queryParams    string
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name:        "成功获取用户列表",
			queryParams: "?page=1&size=10",
			setup: func() {
				users := []*model.User{
					{ID: 1, Username: "user1", RealName: "用户1"},
					{ID: 2, Username: "user2", RealName: "用户2"},
				}
				mockService.On("ListUsers", mock.Anything, 1, 10).Return(users, int64(2), nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `"total":2`,
		},
		{
			name:        "默认分页参数",
			queryParams: "",
			setup: func() {
				users := []*model.User{}
				mockService.On("ListUsers", mock.Anything, 1, 10).Return(users, int64(0), nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   `"total":0`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := setupTestRouter()
			r.GET("/users", handler.ListUsers)

			// 创建请求
			req, _ := http.NewRequest("GET", "/users"+tt.queryParams, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

// 基准测试
func BenchmarkUserHandler_Register(b *testing.B) {
	mockService := new(MockUserService)
	handler := &UserHandler{userService: mockService}

	user := &model.User{ID: 1, Username: "testuser"}
	mockService.On("Register", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("int8")).Return(user, nil)

	r := setupTestRouter()
	r.POST("/register", handler.Register)

	requestBody := RegisterRequest{
		Username: "testuser",
		Password: "password123",
		RealName: "测试用户",
		Phone:    "13800138000",
		Email:    "<EMAIL>",
		UserType: 1,
	}
	body, _ := json.Marshal(requestBody)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("POST", "/register", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
	}
}
