package model

import (
	"time"
)

// User 用户模型
type User struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	Username  string    `gorm:"column:username;uniqueIndex" json:"username"`
	Password  string    `gorm:"column:password" json:"-"`
	RealName  string    `gorm:"column:real_name" json:"realName"`
	Phone     string    `gorm:"column:phone;uniqueIndex" json:"phone"`
	Email     string    `gorm:"column:email" json:"email"`
	UserType  int8      `gorm:"column:user_type" json:"userType"` // 1-拍卖师 2-买家 3-管理员 4-质检员
	Status    int8      `gorm:"column:status" json:"status"`      // 0-禁用 1-启用
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// Role 角色模型
type Role struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name" json:"name"`
	Code        string    `gorm:"column:code;uniqueIndex" json:"code"`
	Description string    `gorm:"column:description" json:"description"`
	Status      int8      `gorm:"column:status" json:"status"` // 0-禁用 1-启用
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "role"
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	UserID    int64     `gorm:"column:user_id;uniqueIndex:uk_user_role" json:"userId"`
	RoleID    int64     `gorm:"column:role_id;uniqueIndex:uk_user_role" json:"roleId"`
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "user_role"
}

// UserWithRoles 用户信息（包含角色）
type UserWithRoles struct {
	User
	Roles []Role `json:"roles"`
}

// Permission 权限模型
type Permission struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`
	Name        string    `gorm:"column:name;size:100;not null" json:"name"`             // 权限名称
	Code        string    `gorm:"column:code;size:100;uniqueIndex;not null" json:"code"` // 权限代码
	Description string    `gorm:"column:description;size:500" json:"description"`        // 权限描述
	Module      string    `gorm:"column:module;size:50" json:"module"`                   // 所属模块
	Action      string    `gorm:"column:action;size:50" json:"action"`                   // 操作类型
	Resource    string    `gorm:"column:resource;size:100" json:"resource"`              // 资源标识
	Status      int8      `gorm:"column:status;default:1" json:"status"`                 // 状态 0-禁用 1-启用
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permission"
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	RoleID       int64     `gorm:"column:role_id;not null" json:"roleId"`
	PermissionID int64     `gorm:"column:permission_id;not null" json:"permissionId"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permission"
}

// RoleWithPermissions 角色信息（包含权限）
type RoleWithPermissions struct {
	Role
	Permissions []Permission `json:"permissions"`
}

// UserPermission 用户权限视图
type UserPermission struct {
	UserID         int64  `json:"userId"`
	Username       string `json:"username"`
	RoleID         int64  `json:"roleId"`
	RoleName       string `json:"roleName"`
	PermissionID   int64  `json:"permissionId"`
	PermissionCode string `json:"permissionCode"`
	Module         string `json:"module"`
	Action         string `json:"action"`
	Resource       string `json:"resource"`
}
