package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// ProductDAO 商品数据访问接口
type ProductDAO interface {
	// 商品管理
	Create(ctx context.Context, product *model.Product) error
	Update(ctx context.Context, product *model.Product) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*model.Product, error)
	List(ctx context.Context, offset, limit int) ([]*model.Product, error)
	Count(ctx context.Context) (int64, error)
	FindByCategory(ctx context.Context, categoryID int64, offset, limit int) ([]*model.Product, error)

	// 分类管理
	CreateCategory(ctx context.Context, category *model.Category) error
	UpdateCategory(ctx context.Context, category *model.Category) error
	DeleteCategory(ctx context.Context, id int64) error
	FindCategoryByID(ctx context.Context, id int64) (*model.Category, error)
	ListCategories(ctx context.Context) ([]*model.Category, error)
	GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error)
}

// productDAO 商品数据访问实现
type productDAO struct {
	db *gorm.DB
}

// NewProductDAO 创建商品数据访问实例
func NewProductDAO() ProductDAO {
	return &productDAO{
		db: GetProductDB(),
	}
}

// Create 创建商品
func (d *productDAO) Create(ctx context.Context, product *model.Product) error {
	return d.db.WithContext(ctx).Create(product).Error
}

// Update 更新商品
func (d *productDAO) Update(ctx context.Context, product *model.Product) error {
	return d.db.WithContext(ctx).Save(product).Error
}

// Delete 删除商品
func (d *productDAO) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Product{}, id).Error
}

// FindByID 根据ID查找商品
func (d *productDAO) FindByID(ctx context.Context, id int64) (*model.Product, error) {
	var product model.Product
	err := d.db.WithContext(ctx).First(&product, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &product, nil
}

// List 分页查询商品列表
func (d *productDAO) List(ctx context.Context, offset, limit int) ([]*model.Product, error) {
	var products []*model.Product
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&products).Error
	return products, err
}

// Count 统计商品总数
func (d *productDAO) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Product{}).Count(&count).Error
	return count, err
}

// FindByCategory 根据分类查询商品
func (d *productDAO) FindByCategory(ctx context.Context, categoryID int64, offset, limit int) ([]*model.Product, error) {
	var products []*model.Product
	err := d.db.WithContext(ctx).Where("category_id = ?", categoryID).Offset(offset).Limit(limit).Find(&products).Error
	return products, err
}

// CreateCategory 创建分类
func (d *productDAO) CreateCategory(ctx context.Context, category *model.Category) error {
	return d.db.WithContext(ctx).Create(category).Error
}

// UpdateCategory 更新分类
func (d *productDAO) UpdateCategory(ctx context.Context, category *model.Category) error {
	return d.db.WithContext(ctx).Save(category).Error
}

// DeleteCategory 删除分类
func (d *productDAO) DeleteCategory(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Category{}, id).Error
}

// FindCategoryByID 根据ID查找分类
func (d *productDAO) FindCategoryByID(ctx context.Context, id int64) (*model.Category, error) {
	var category model.Category
	err := d.db.WithContext(ctx).First(&category, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &category, nil
}

// ListCategories 查询所有分类
func (d *productDAO) ListCategories(ctx context.Context) ([]*model.Category, error) {
	var categories []*model.Category
	err := d.db.WithContext(ctx).Order("sort_order ASC").Find(&categories).Error
	return categories, err
}

// GetCategoryTree 获取分类树
func (d *productDAO) GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error) {
	var categories []*model.Category
	err := d.db.WithContext(ctx).Order("sort_order ASC").Find(&categories).Error
	if err != nil {
		return nil, err
	}

	// 构建树形结构
	categoryMap := make(map[int64]*model.CategoryTree)
	var roots []model.CategoryTree

	// 先创建所有节点
	for _, cat := range categories {
		tree := model.CategoryTree{
			Category: *cat,
			Children: []model.CategoryTree{},
		}
		categoryMap[cat.ID] = &tree
	}

	// 构建父子关系
	for _, cat := range categories {
		if cat.ParentID == nil {
			// 根节点
			roots = append(roots, *categoryMap[cat.ID])
		} else {
			// 子节点
			if parent, exists := categoryMap[*cat.ParentID]; exists {
				parent.Children = append(parent.Children, *categoryMap[cat.ID])
			}
		}
	}

	return roots, nil
}
