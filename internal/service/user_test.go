package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"golang.org/x/crypto/bcrypt"
)

// MockUserDAO 模拟用户DAO
type MockUserDAO struct {
	mock.Mock
}

func (m *MockUserDAO) Create(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserDAO) Update(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserDAO) Delete(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserDAO) FindByID(ctx context.Context, id int64) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserDAO) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserDAO) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	args := m.Called(ctx, phone)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserDAO) FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserWithRoles), args.Error(1)
}

func (m *MockUserDAO) List(ctx context.Context, offset, limit int) ([]*model.User, error) {
	args := m.Called(ctx, offset, limit)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockUserDAO) Count(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func TestUserService_Register(t *testing.T) {
	mockDAO := new(MockUserDAO)
	service := &userService{userDAO: mockDAO}

	tests := []struct {
		name     string
		username string
		password string
		realName string
		phone    string
		email    string
		userType int8
		setup    func()
		wantErr  bool
		errType  error
	}{
		{
			name:     "成功注册用户",
			username: "testuser",
			password: "password123",
			realName: "测试用户",
			phone:    "13800138000",
			email:    "<EMAIL>",
			userType: 1,
			setup: func() {
				mockDAO.On("FindByUsername", mock.Anything, "testuser").Return(nil, nil)
				mockDAO.On("FindByPhone", mock.Anything, "13800138000").Return(nil, nil)
				mockDAO.On("Create", mock.Anything, mock.AnythingOfType("*model.User")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:     "用户名已存在",
			username: "existuser",
			password: "password123",
			realName: "测试用户",
			phone:    "13800138001",
			email:    "<EMAIL>",
			userType: 1,
			setup: func() {
				existUser := &model.User{ID: 1, Username: "existuser"}
				mockDAO.On("FindByUsername", mock.Anything, "existuser").Return(existUser, nil)
			},
			wantErr: true,
			errType: ErrUserAlreadyExists,
		},
		{
			name:     "手机号已存在",
			username: "newuser",
			password: "password123",
			realName: "测试用户",
			phone:    "13800138000",
			email:    "<EMAIL>",
			userType: 1,
			setup: func() {
				mockDAO.On("FindByUsername", mock.Anything, "newuser").Return(nil, nil)
				existUser := &model.User{ID: 2, Phone: "13800138000"}
				mockDAO.On("FindByPhone", mock.Anything, "13800138000").Return(existUser, nil)
			},
			wantErr: true,
			errType: ErrUserAlreadyExists,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockDAO.ExpectedCalls = nil
			mockDAO.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			user, err := service.Register(context.Background(), tt.username, tt.password, tt.realName, tt.phone, tt.email, tt.userType)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType, err)
				}
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, user)
				assert.Equal(t, tt.username, user.Username)
				assert.Equal(t, tt.realName, user.RealName)
				assert.Equal(t, tt.phone, user.Phone)
				assert.Equal(t, tt.email, user.Email)
				assert.Equal(t, tt.userType, user.UserType)
				assert.Equal(t, int8(1), user.Status) // 默认启用
			}

			mockDAO.AssertExpectations(t)
		})
	}
}

func TestUserService_Login(t *testing.T) {
	mockDAO := new(MockUserDAO)
	service := &userService{userDAO: mockDAO}

	// 创建测试用户密码哈希
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)

	tests := []struct {
		name     string
		username string
		password string
		setup    func()
		wantErr  bool
		errType  error
	}{
		{
			name:     "成功登录",
			username: "testuser",
			password: "password123",
			setup: func() {
				user := &model.User{
					ID:       1,
					Username: "testuser",
					Password: string(hashedPassword),
					Status:   1,
				}
				userWithRoles := &model.UserWithRoles{
					User:  *user,
					Roles: []model.Role{},
				}
				mockDAO.On("FindByUsername", mock.Anything, "testuser").Return(user, nil)
				mockDAO.On("FindWithRoles", mock.Anything, int64(1)).Return(userWithRoles, nil)
			},
			wantErr: false,
		},
		{
			name:     "用户不存在",
			username: "nonexist",
			password: "password123",
			setup: func() {
				mockDAO.On("FindByUsername", mock.Anything, "nonexist").Return(nil, nil)
			},
			wantErr: true,
			errType: ErrUserNotFound,
		},
		{
			name:     "密码错误",
			username: "testuser",
			password: "wrongpassword",
			setup: func() {
				user := &model.User{
					ID:       1,
					Username: "testuser",
					Password: string(hashedPassword),
					Status:   1,
				}
				mockDAO.On("FindByUsername", mock.Anything, "testuser").Return(user, nil)
			},
			wantErr: true,
			errType: ErrInvalidPassword,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockDAO.ExpectedCalls = nil
			mockDAO.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			userWithRoles, err := service.Login(context.Background(), tt.username, tt.password)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType, err)
				}
				assert.Nil(t, userWithRoles)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, userWithRoles)
				assert.Equal(t, tt.username, userWithRoles.Username)
			}

			mockDAO.AssertExpectations(t)
		})
	}
}

func TestUserService_ChangePassword(t *testing.T) {
	mockDAO := new(MockUserDAO)
	service := &userService{userDAO: mockDAO}

	// 创建测试用户密码哈希
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("oldpassword"), bcrypt.DefaultCost)

	tests := []struct {
		name        string
		userID      int64
		oldPassword string
		newPassword string
		setup       func()
		wantErr     bool
		errType     error
	}{
		{
			name:        "成功修改密码",
			userID:      1,
			oldPassword: "oldpassword",
			newPassword: "newpassword123",
			setup: func() {
				user := &model.User{
					ID:       1,
					Username: "testuser",
					Password: string(hashedPassword),
				}
				mockDAO.On("FindByID", mock.Anything, int64(1)).Return(user, nil)
				mockDAO.On("Update", mock.Anything, mock.AnythingOfType("*model.User")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:        "用户不存在",
			userID:      999,
			oldPassword: "oldpassword",
			newPassword: "newpassword123",
			setup: func() {
				mockDAO.On("FindByID", mock.Anything, int64(999)).Return(nil, nil)
			},
			wantErr: true,
			errType: ErrUserNotFound,
		},
		{
			name:        "原密码错误",
			userID:      1,
			oldPassword: "wrongpassword",
			newPassword: "newpassword123",
			setup: func() {
				user := &model.User{
					ID:       1,
					Username: "testuser",
					Password: string(hashedPassword),
				}
				mockDAO.On("FindByID", mock.Anything, int64(1)).Return(user, nil)
			},
			wantErr: true,
			errType: ErrInvalidPassword,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockDAO.ExpectedCalls = nil
			mockDAO.Calls = nil
			
			if tt.setup != nil {
				tt.setup()
			}

			err := service.ChangePassword(context.Background(), tt.userID, tt.oldPassword, tt.newPassword)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType, err)
				}
			} else {
				assert.NoError(t, err)
			}

			mockDAO.AssertExpectations(t)
		})
	}
}

// 基准测试
func BenchmarkUserService_Register(b *testing.B) {
	mockDAO := new(MockUserDAO)
	service := &userService{userDAO: mockDAO}

	mockDAO.On("FindByUsername", mock.Anything, mock.AnythingOfType("string")).Return(nil, nil)
	mockDAO.On("FindByPhone", mock.Anything, mock.AnythingOfType("string")).Return(nil, nil)
	mockDAO.On("Create", mock.Anything, mock.AnythingOfType("*model.User")).Return(nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service.Register(context.Background(), "testuser", "password123", "测试用户", "13800138000", "<EMAIL>", 1)
	}
}
