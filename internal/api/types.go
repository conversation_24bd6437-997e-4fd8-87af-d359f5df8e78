package api

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error string `json:"error"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	Message string `json:"message"`
}

// PageResponse 分页响应
type PageResponse struct {
	List  interface{} `json:"list"`
	Total int64       `json:"total"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
}

// ParseParamID 从 URL 参数中解析 ID
func ParseParamID(c *gin.Context, param string) (int64, error) {
	id := c.Param(param)
	if id == "" {
		return 0, fmt.Errorf("missing %s parameter", param)
	}
	return strconv.ParseInt(id, 10, 64)
}

// ParsePagination 解析分页参数
func ParsePagination(c *gin.Context) (page, size int) {
	page, _ = strconv.Atoi(c<PERSON>("page", "1"))
	if page < 1 {
		page = 1
	}

	size, _ = strconv.Atoi(c<PERSON><PERSON><PERSON>("size", "10"))
	if size < 1 {
		size = 10
	}
	if size > 100 {
		size = 100
	}

	return page, size
}
