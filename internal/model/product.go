package model

import (
	"time"
)

// Product 商品模型
type Product struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	Name         string    `gorm:"column:name" json:"name"`
	CategoryID   int64     `gorm:"column:category_id" json:"categoryId"`
	Description  string    `gorm:"column:description" json:"description"`
	QualityLevel int8      `gorm:"column:quality_level" json:"qualityLevel"` // 1-优 2-良 3-中
	Origin       string    `gorm:"column:origin" json:"origin"`
	SupplierID   int64     `gorm:"column:supplier_id" json:"supplierId"`
	Status       int8      `gorm:"column:status" json:"status"` // 0-下架 1-上架
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "product"
}

// Category 商品类别模型
type Category struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	Name      string    `gorm:"column:name" json:"name"`
	ParentID  *int64    `gorm:"column:parent_id" json:"parentId"`
	Level     int8      `gorm:"column:level" json:"level"`
	SortOrder int       `gorm:"column:sort_order" json:"sortOrder"`
	Status    int8      `gorm:"column:status" json:"status"` // 0-禁用 1-启用
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Category) TableName() string {
	return "category"
}

// CategoryTree 商品类别树形结构
type CategoryTree struct {
	Category
	Children []CategoryTree `json:"children"`
}

// ProductWithCategory 商品信息（包含类别）
type ProductWithCategory struct {
	Product
	Category Category `json:"category"`
}
