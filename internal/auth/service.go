package auth

import (
	"context"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/viper"
)

var (
	ErrInvalidCredentials = errors.New("用户名或密码错误")
	ErrTokenExpired      = errors.New("令牌已过期")
	ErrTokenInvalid      = errors.New("无效的令牌")
)

// Service 认证服务接口
type Service interface {
	// 令牌管理
	GenerateToken(userID int64, username string, userType int8, roles []int64) (string, error)
	ParseToken(tokenString string) (*JWTClaims, error)
	RefreshToken(ctx context.Context, token string) (string, error)
	ValidateToken(tokenString string) (*TokenInfo, error)
	
	// 认证相关
	Login(ctx context.Context, username, password string) (*LoginResponse, error)
	Logout(ctx context.Context, token string) error
	
	// 密码管理
	ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, username string) (string, error)
}

// service 认证服务实现
type service struct {
	userService UserService // 用户服务接口，需要从外部注入
}

// NewService 创建认证服务实例
func NewService(userService UserService) Service {
	return &service{
		userService: userService,
	}
}

// GenerateToken 生成JWT令牌
func (s *service) GenerateToken(userID int64, username string, userType int8, roles []int64) (string, error) {
	secret := viper.GetString("jwt.secret")
	if secret == "" {
		secret = "flower_auction_secret_key"
	}

	expireHours := viper.GetInt("jwt.expireHours")
	if expireHours == 0 {
		expireHours = 24
	}

	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		UserType: userType,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expireHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "flower-auction",
			Subject:   username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

// ParseToken 解析JWT令牌
func (s *service) ParseToken(tokenString string) (*JWTClaims, error) {
	secret := viper.GetString("jwt.secret")
	if secret == "" {
		secret = "flower_auction_secret_key"
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, ErrTokenInvalid
}

// RefreshToken 刷新令牌
func (s *service) RefreshToken(ctx context.Context, token string) (string, error) {
	claims, err := s.ParseToken(token)
	if err != nil {
		return "", err
	}

	// 检查token是否即将过期（1小时内）
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return token, nil // 不需要刷新
	}

	// 生成新token
	return s.GenerateToken(claims.UserID, claims.Username, claims.UserType, claims.Roles)
}

// ValidateToken 验证令牌
func (s *service) ValidateToken(tokenString string) (*TokenInfo, error) {
	claims, err := s.ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 检查是否过期
	if time.Now().After(claims.ExpiresAt.Time) {
		return nil, ErrTokenExpired
	}

	return &TokenInfo{
		UserID:    claims.UserID,
		Username:  claims.Username,
		UserType:  claims.UserType,
		Roles:     claims.Roles,
		IssuedAt:  claims.IssuedAt.Time,
		ExpiresAt: claims.ExpiresAt.Time,
	}, nil
}

// Login 用户登录
func (s *service) Login(ctx context.Context, username, password string) (*LoginResponse, error) {
	// 验证用户凭据
	user, err := s.userService.ValidateCredentials(ctx, username, password)
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	// 检查用户状态
	if !s.userService.IsUserActive(user) {
		return nil, errors.New("用户已被禁用")
	}

	// 获取用户角色
	roles, err := s.userService.GetUserRoles(ctx, user.GetID())
	if err != nil {
		return nil, err
	}

	// 生成JWT令牌
	token, err := s.GenerateToken(user.GetID(), user.GetUsername(), user.GetUserType(), roles)
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

// Logout 用户登出
func (s *service) Logout(ctx context.Context, token string) error {
	// 这里可以实现令牌黑名单机制
	// 目前简单返回成功
	return nil
}

// ChangePassword 修改密码
func (s *service) ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error {
	return s.userService.ChangePassword(ctx, userID, oldPassword, newPassword)
}

// ResetPassword 重置密码
func (s *service) ResetPassword(ctx context.Context, username string) (string, error) {
	return s.userService.ResetPassword(ctx, username)
}

// UserService 用户服务接口（需要外部实现）
type UserService interface {
	ValidateCredentials(ctx context.Context, username, password string) (User, error)
	IsUserActive(user User) bool
	GetUserRoles(ctx context.Context, userID int64) ([]int64, error)
	ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, username string) (string, error)
}

// User 用户接口（需要外部实现）
type User interface {
	GetID() int64
	GetUsername() string
	GetUserType() int8
}
