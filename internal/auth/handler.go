package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Handler 认证接口处理器
type Handler struct {
	authService Service
}

// NewHandler 创建认证接口处理器
func NewHandler(authService Service) *Handler {
	return &Handler{
		authService: authService,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(r *gin.Engine) {
	auth := r.Group("/api/v1/auth")
	{
		auth.POST("/login", h.<PERSON><PERSON>)                    // 用户登录
		auth.POST("/logout", h.Logout)                  // 用户登出
		auth.POST("/refresh", h.RefreshToken)           // 刷新令牌
		auth.POST("/change-password", h.ChangePassword) // 修改密码
		auth.POST("/reset-password", h.ResetPassword)   // 重置密码
		auth.GET("/me", h.GetCurrentUser)               // 获取当前用户信息
		auth.POST("/validate", h.ValidateToken)         // 验证令牌
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录信息"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "用户名或密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	response, err := h.authService.Login(c.Request.Context(), req.Username, req.Password)
	if err != nil {
		if err == ErrInvalidCredentials {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户名或密码错误"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出，使令牌失效
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LogoutRequest true "登出信息"
// @Success 200 {object} SuccessResponse "登出成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/logout [post]
func (h *Handler) Logout(c *gin.Context) {
	var req LogoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.authService.Logout(c.Request.Context(), req.Token); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "登出成功"})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 刷新访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "刷新令牌信息"
// @Success 200 {object} RefreshTokenResponse "刷新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "令牌无效"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/refresh [post]
func (h *Handler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	newToken, err := h.authService.RefreshToken(c.Request.Context(), req.Token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, RefreshTokenResponse{Token: newToken})
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改用户密码
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body ChangePasswordRequest true "密码信息"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "原密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/change-password [post]
func (h *Handler) ChangePassword(c *gin.Context) {
	userID, ok := GetCurrentUserID(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.authService.ChangePassword(c.Request.Context(), userID, req.OldPassword, req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "密码修改成功"})
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Username string `json:"username" binding:"required"`
}

// ResetPasswordResponse 重置密码响应
type ResetPasswordResponse struct {
	TempPassword string `json:"tempPassword"`
	Message      string `json:"message"`
}

// ResetPassword 重置密码
// @Summary 重置密码
// @Description 重置用户密码
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body ResetPasswordRequest true "用户名"
// @Success 200 {object} ResetPasswordResponse "重置成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/reset-password [post]
func (h *Handler) ResetPassword(c *gin.Context) {
	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	tempPassword, err := h.authService.ResetPassword(c.Request.Context(), req.Username)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, ResetPasswordResponse{
		TempPassword: tempPassword,
		Message:      "密码重置成功，请使用临时密码登录后及时修改密码",
	})
}

// GetCurrentUser 获取当前用户信息
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的信息
// @Tags 认证
// @Accept json
// @Produce json
// @Success 200 {object} TokenInfo "用户信息"
// @Failure 401 {object} ErrorResponse "未授权"
// @Router /api/v1/auth/me [get]
func (h *Handler) GetCurrentUser(c *gin.Context) {
	userID, username, userType, roles, ok := GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"userId":   userID,
		"username": username,
		"userType": userType,
		"roles":    roles,
	})
}

// ValidateTokenRequest 验证令牌请求
type ValidateTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// ValidateToken 验证令牌
// @Summary 验证令牌
// @Description 验证访问令牌的有效性
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body ValidateTokenRequest true "令牌信息"
// @Success 200 {object} TokenInfo "令牌信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "令牌无效"
// @Router /api/v1/auth/validate [post]
func (h *Handler) ValidateToken(c *gin.Context) {
	var req ValidateTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	tokenInfo, err := h.authService.ValidateToken(req.Token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, tokenInfo)
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error string `json:"error"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	Message string `json:"message"`
}
