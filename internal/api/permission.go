package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/service"
)

// PermissionHandler 权限管理接口处理器
type PermissionHandler struct {
	permissionService service.PermissionService
}

// NewPermissionHandler 创建权限管理接口处理器
func NewPermissionHandler() *PermissionHandler {
	return &PermissionHandler{
		permissionService: service.NewPermissionService(),
	}
}

// RegisterRoutes 注册路由
func (h *PermissionHandler) RegisterRoutes(r *gin.Engine) {
	// 权限管理路由组
	permission := r.Group("/api/v1/permissions")
	permission.Use(middleware.JWTAuth())
	{
		permission.POST("", middleware.RequireRole(1), h.CreatePermission)                    // 创建权限 (仅管理员)
		permission.PUT("/:id", middleware.RequireRole(1), h.UpdatePermission)                // 更新权限 (仅管理员)
		permission.DELETE("/:id", middleware.RequireRole(1), h.DeletePermission)             // 删除权限 (仅管理员)
		permission.GET("/:id", middleware.RequireRole(1, 2), h.GetPermission)                // 获取权限详情
		permission.GET("", middleware.RequireRole(1, 2), h.ListPermissions)                  // 权限列表
		permission.PUT("/:id/status", middleware.RequireRole(1), h.UpdatePermissionStatus)   // 更新权限状态
		permission.POST("/init", middleware.RequireRole(1), h.InitDefaultPermissions)        // 初始化默认权限
	}

	// 角色权限管理路由组
	rolePermission := r.Group("/api/v1/roles/:roleId/permissions")
	rolePermission.Use(middleware.JWTAuth())
	{
		rolePermission.POST("", middleware.RequireRole(1), h.AssignPermissionsToRole)        // 分配权限给角色
		rolePermission.DELETE("", middleware.RequireRole(1), h.RemovePermissionsFromRole)    // 移除角色权限
		rolePermission.GET("", middleware.RequireRole(1, 2), h.GetRolePermissions)           // 获取角色权限
	}

	// 用户权限查询路由组
	userPermission := r.Group("/api/v1/users/:userId/permissions")
	userPermission.Use(middleware.JWTAuth())
	{
		userPermission.GET("", h.GetUserPermissions)                                         // 获取用户权限
		userPermission.GET("/check/:permissionCode", h.CheckUserPermission)                 // 检查用户权限
		userPermission.GET("/modules/:module", h.GetUserPermissionsByModule)               // 按模块获取用户权限
	}
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	Description string `json:"description"`
	Module      string `json:"module" binding:"required"`
	Action      string `json:"action" binding:"required"`
	Resource    string `json:"resource" binding:"required"`
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	permission, err := h.permissionService.CreatePermission(c.Request.Context(), req.Name, req.Code, req.Description, req.Module, req.Action, req.Resource)
	if err != nil {
		if err == service.ErrPermissionCodeExists {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "权限代码已存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Module      string `json:"module" binding:"required"`
	Action      string `json:"action" binding:"required"`
	Resource    string `json:"resource" binding:"required"`
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.UpdatePermission(c.Request.Context(), permissionID, req.Name, req.Description, req.Module, req.Action, req.Resource); err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限更新成功"})
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.DeletePermission(c.Request.Context(), permissionID); err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限删除成功"})
}

// GetPermission 获取权限详情
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	permission, err := h.permissionService.GetPermission(c.Request.Context(), permissionID)
	if err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// ListPermissions 权限列表
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	page, size := ParsePagination(c)
	module := c.Query("module")

	permissions, total, err := h.permissionService.ListPermissions(c.Request.Context(), module, page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  permissions,
		Total: total,
		Page:  page,
		Size:  size,
	})
}

// UpdatePermissionStatusRequest 更新权限状态请求
type UpdatePermissionStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"`
}

// UpdatePermissionStatus 更新权限状态
func (h *PermissionHandler) UpdatePermissionStatus(c *gin.Context) {
	permissionID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdatePermissionStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.UpdatePermissionStatus(c.Request.Context(), permissionID, req.Status); err != nil {
		if err == service.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "权限不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限状态更新成功"})
}

// InitDefaultPermissions 初始化默认权限
func (h *PermissionHandler) InitDefaultPermissions(c *gin.Context) {
	if err := h.permissionService.InitDefaultPermissions(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "默认权限初始化成功"})
}

// AssignPermissionsRequest 分配权限请求
type AssignPermissionsRequest struct {
	PermissionIDs []int64 `json:"permissionIds" binding:"required"`
}

// AssignPermissionsToRole 分配权限给角色
func (h *PermissionHandler) AssignPermissionsToRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "roleId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.AssignPermissionsToRole(c.Request.Context(), roleID, req.PermissionIDs); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限分配成功"})
}

// RemovePermissionsFromRole 移除角色权限
func (h *PermissionHandler) RemovePermissionsFromRole(c *gin.Context) {
	roleID, err := ParseParamID(c, "roleId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AssignPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.permissionService.RemovePermissionsFromRole(c.Request.Context(), roleID, req.PermissionIDs); err != nil {
		if err == service.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "权限移除成功"})
}

// GetRolePermissions 获取角色权限
func (h *PermissionHandler) GetRolePermissions(c *gin.Context) {
	roleID, err := ParseParamID(c, "roleId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	roleWithPermissions, err := h.permissionService.GetRoleWithPermissions(c.Request.Context(), roleID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if roleWithPermissions == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "角色不存在"})
		return
	}

	c.JSON(http.StatusOK, roleWithPermissions)
}

// GetUserPermissions 获取用户权限
func (h *PermissionHandler) GetUserPermissions(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 检查权限：只能查看自己的权限或管理员可以查看所有用户权限
	currentUserID, _, _, roles, ok := middleware.GetCurrentUser(c)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	// 检查是否是管理员或查看自己的权限
	isAdmin := false
	for _, role := range roles {
		if role == 1 { // 管理员角色
			isAdmin = true
			break
		}
	}

	if !isAdmin && currentUserID != userID {
		c.JSON(http.StatusForbidden, ErrorResponse{Error: "权限不足"})
		return
	}

	userPermissions, err := h.permissionService.GetUserPermissions(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, userPermissions)
}

// CheckUserPermission 检查用户权限
func (h *PermissionHandler) CheckUserPermission(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	permissionCode := c.Param("permissionCode")
	if permissionCode == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "权限代码不能为空"})
		return
	}

	hasPermission, err := h.permissionService.CheckUserPermission(c.Request.Context(), userID, permissionCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"hasPermission": hasPermission,
		"permissionCode": permissionCode,
	})
}

// GetUserPermissionsByModule 按模块获取用户权限
func (h *PermissionHandler) GetUserPermissionsByModule(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	module := c.Param("module")
	if module == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "模块名称不能为空"})
		return
	}

	permissions, err := h.permissionService.GetUserPermissionsByModule(c.Request.Context(), userID, module)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, permissions)
}
