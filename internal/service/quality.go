package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrBatchNotFound     = errors.New("批次不存在")
	ErrQualityCheckNotFound = errors.New("质检记录不存在")
	ErrBatchAlreadyChecked = errors.New("批次已质检")
)

// QualityService 质检服务接口
type QualityService interface {
	// 批次管理
	CreateBatch(ctx context.Context, batchNo string, productID, supplierID int64, quantity int, unit string) (*model.Batch, error)
	UpdateBatch(ctx context.Context, id int64, quantity int, qualityLevel int8, status int8) error
	GetBatch(ctx context.Context, id int64) (*model.BatchWithQuality, error)
	GetBatchByNo(ctx context.Context, batchNo string) (*model.BatchWithQuality, error)
	ListBatches(ctx context.Context, status int8, page, size int) ([]*model.BatchWithQuality, int64, error)

	// 质检管理
	StartQualityCheck(ctx context.Context, batchID, inspectorID int64) (*model.QualityCheck, error)
	UpdateQualityCheck(ctx context.Context, id int64, actualQty int, qualityLevel int8, defectRate float64, remarks string) error
	AddDefect(ctx context.Context, checkID int64, defectType, defectCode string, severity int8, description, imageURL string) error
	FinishQualityCheck(ctx context.Context, id int64) error
	GetQualityCheck(ctx context.Context, id int64) (*model.QualityCheckDetail, error)
	ListQualityChecks(ctx context.Context, status int8, page, size int) ([]*model.QualityCheckDetail, int64, error)

	// 质检标准管理
	CreateQualityStandard(ctx context.Context, categoryID int64, standardName string, level int8, defectRate float64, description string) (*model.QualityStandard, error)
	UpdateQualityStandard(ctx context.Context, id int64, standardName string, defectRate float64, description string) error
	GetQualityStandard(ctx context.Context, categoryID int64, level int8) (*model.QualityStandard, error)
	ListQualityStandards(ctx context.Context, categoryID int64) ([]*model.QualityStandard, error)
}

// qualityService 质检服务实现
type qualityService struct {
	qualityDAO dao.QualityDAO
	productDAO dao.ProductDAO
}

// NewQualityService 创建质检服务实例
func NewQualityService() QualityService {
	return &qualityService{
		qualityDAO: dao.NewQualityDAO(),
		productDAO: dao.NewProductDAO(),
	}
}

// CreateBatch 创建批次
func (s *qualityService) CreateBatch(ctx context.Context, batchNo string, productID, supplierID int64, quantity int, unit string) (*model.Batch, error) {
	// 检查批次号是否已存在
	existBatch, err := s.qualityDAO.FindBatchByNo(ctx, batchNo)
	if err != nil {
		return nil, err
	}
	if existBatch != nil {
		return nil, errors.New("批次号已存在")
	}

	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}

	batch := &model.Batch{
		BatchNo:     batchNo,
		ProductID:   productID,
		SupplierID:  supplierID,
		Quantity:    quantity,
		Unit:        unit,
		Status:      0, // 待入库
		ArrivalTime: time.Now(),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.qualityDAO.CreateBatch(ctx, batch); err != nil {
		return nil, err
	}

	return batch, nil
}

// UpdateBatch 更新批次
func (s *qualityService) UpdateBatch(ctx context.Context, id int64, quantity int, qualityLevel int8, status int8) error {
	batch, err := s.qualityDAO.FindBatchByID(ctx, id)
	if err != nil {
		return err
	}
	if batch == nil {
		return ErrBatchNotFound
	}

	batch.Quantity = quantity
	batch.QualityLevel = qualityLevel
	batch.Status = status
	batch.UpdatedAt = time.Now()

	return s.qualityDAO.UpdateBatch(ctx, batch)
}

// GetBatch 获取批次信息
func (s *qualityService) GetBatch(ctx context.Context, id int64) (*model.BatchWithQuality, error) {
	return s.qualityDAO.FindBatchWithQualityByID(ctx, id)
}

// GetBatchByNo 根据批次号获取批次信息
func (s *qualityService) GetBatchByNo(ctx context.Context, batchNo string) (*model.BatchWithQuality, error) {
	return s.qualityDAO.FindBatchWithQualityByNo(ctx, batchNo)
}

// ListBatches 查询批次列表
func (s *qualityService) ListBatches(ctx context.Context, status int8, page, size int) ([]*model.BatchWithQuality, int64, error) {
	offset := (page - 1) * size
	batches, err := s.qualityDAO.ListBatchesWithQuality(ctx, status, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.qualityDAO.CountBatches(ctx, status)
	if err != nil {
		return nil, 0, err
	}

	return batches, total, nil
}

// StartQualityCheck 开始质检
func (s *qualityService) StartQualityCheck(ctx context.Context, batchID, inspectorID int64) (*model.QualityCheck, error) {
	// 检查批次是否存在
	batch, err := s.qualityDAO.FindBatchByID(ctx, batchID)
	if err != nil {
		return nil, err
	}
	if batch == nil {
		return nil, ErrBatchNotFound
	}

	// 检查是否已经质检
	existCheck, err := s.qualityDAO.FindQualityCheckByBatch(ctx, batchID)
	if err != nil {
		return nil, err
	}
	if existCheck != nil {
		return nil, ErrBatchAlreadyChecked
	}

	qualityCheck := &model.QualityCheck{
		BatchID:     batchID,
		ProductID:   batch.ProductID,
		InspectorID: inspectorID,
		OriginalQty: batch.Quantity,
		ActualQty:   batch.Quantity,
		Status:      0, // 待质检
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.qualityDAO.CreateQualityCheck(ctx, qualityCheck); err != nil {
		return nil, err
	}

	// 更新批次状态为质检中
	batch.Status = 2
	batch.UpdatedAt = time.Now()
	if err := s.qualityDAO.UpdateBatch(ctx, batch); err != nil {
		return nil, err
	}

	return qualityCheck, nil
}

// UpdateQualityCheck 更新质检记录
func (s *qualityService) UpdateQualityCheck(ctx context.Context, id int64, actualQty int, qualityLevel int8, defectRate float64, remarks string) error {
	check, err := s.qualityDAO.FindQualityCheckByID(ctx, id)
	if err != nil {
		return err
	}
	if check == nil {
		return ErrQualityCheckNotFound
	}

	check.ActualQty = actualQty
	check.QualityLevel = qualityLevel
	check.DefectRate = defectRate
	check.Remarks = remarks
	check.UpdatedAt = time.Now()

	return s.qualityDAO.UpdateQualityCheck(ctx, check)
}

// AddDefect 添加瑕疵记录
func (s *qualityService) AddDefect(ctx context.Context, checkID int64, defectType, defectCode string, severity int8, description, imageURL string) error {
	defect := &model.QualityDefect{
		CheckID:     checkID,
		DefectType:  defectType,
		DefectCode:  defectCode,
		Severity:    severity,
		Description: description,
		ImageURL:    imageURL,
		CreatedAt:   time.Now(),
	}

	return s.qualityDAO.CreateQualityDefect(ctx, defect)
}

// FinishQualityCheck 完成质检
func (s *qualityService) FinishQualityCheck(ctx context.Context, id int64) error {
	check, err := s.qualityDAO.FindQualityCheckByID(ctx, id)
	if err != nil {
		return err
	}
	if check == nil {
		return ErrQualityCheckNotFound
	}

	// 更新质检状态
	check.Status = 1 // 已质检
	check.UpdatedAt = time.Now()
	if err := s.qualityDAO.UpdateQualityCheck(ctx, check); err != nil {
		return err
	}

	// 更新批次状态和质量等级
	batch, err := s.qualityDAO.FindBatchByID(ctx, check.BatchID)
	if err != nil {
		return err
	}
	if batch != nil {
		batch.Status = 3 // 已质检
		batch.QualityLevel = check.QualityLevel
		batch.Quantity = check.ActualQty
		batch.UpdatedAt = time.Now()
		if err := s.qualityDAO.UpdateBatch(ctx, batch); err != nil {
			return err
		}
	}

	return nil
}

// GetQualityCheck 获取质检详情
func (s *qualityService) GetQualityCheck(ctx context.Context, id int64) (*model.QualityCheckDetail, error) {
	return s.qualityDAO.FindQualityCheckDetailByID(ctx, id)
}

// ListQualityChecks 查询质检记录列表
func (s *qualityService) ListQualityChecks(ctx context.Context, status int8, page, size int) ([]*model.QualityCheckDetail, int64, error) {
	offset := (page - 1) * size
	checks, err := s.qualityDAO.ListQualityCheckDetails(ctx, status, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.qualityDAO.CountQualityChecks(ctx, status)
	if err != nil {
		return nil, 0, err
	}

	return checks, total, nil
}

// CreateQualityStandard 创建质检标准
func (s *qualityService) CreateQualityStandard(ctx context.Context, categoryID int64, standardName string, level int8, defectRate float64, description string) (*model.QualityStandard, error) {
	standard := &model.QualityStandard{
		CategoryID:   categoryID,
		StandardName: standardName,
		Level:        level,
		DefectRate:   defectRate,
		Description:  description,
		Status:       1, // 启用
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.qualityDAO.CreateQualityStandard(ctx, standard); err != nil {
		return nil, err
	}

	return standard, nil
}

// UpdateQualityStandard 更新质检标准
func (s *qualityService) UpdateQualityStandard(ctx context.Context, id int64, standardName string, defectRate float64, description string) error {
	standard, err := s.qualityDAO.FindQualityStandardByID(ctx, id)
	if err != nil {
		return err
	}
	if standard == nil {
		return errors.New("质检标准不存在")
	}

	standard.StandardName = standardName
	standard.DefectRate = defectRate
	standard.Description = description
	standard.UpdatedAt = time.Now()

	return s.qualityDAO.UpdateQualityStandard(ctx, standard)
}

// GetQualityStandard 获取质检标准
func (s *qualityService) GetQualityStandard(ctx context.Context, categoryID int64, level int8) (*model.QualityStandard, error) {
	return s.qualityDAO.FindQualityStandardByCategoryAndLevel(ctx, categoryID, level)
}

// ListQualityStandards 查询质检标准列表
func (s *qualityService) ListQualityStandards(ctx context.Context, categoryID int64) ([]*model.QualityStandard, error) {
	return s.qualityDAO.ListQualityStandardsByCategory(ctx, categoryID)
}
