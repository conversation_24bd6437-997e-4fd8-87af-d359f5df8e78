package service

import (
	"context"
	"errors"
	"mime/multipart"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrProductImageNotFound = errors.New("商品图片不存在")
	ErrProductSpecNotFound  = errors.New("商品规格不存在")
	ErrInsufficientStock    = errors.New("库存不足")
	ErrProductAuditNotFound = errors.New("商品审核记录不存在")
)

// ProductEnhancedService 商品增强功能服务接口
type ProductEnhancedService interface {
	// 商品图片管理
	UploadProductImages(ctx context.Context, productID int64, files []*multipart.FileHeader, imageType int8) ([]string, error)
	GetProductImages(ctx context.Context, productID int64, imageType int8) ([]*model.ProductImage, error)
	DeleteProductImage(ctx context.Context, imageID int64) error
	UpdateImageOrder(ctx context.Context, imageID int64, sortOrder int) error

	// 商品规格管理
	CreateProductSpec(ctx context.Context, productID int64, specName, specValue, sku string, price float64, stock int, weight, volume float64) (*model.ProductSpec, error)
	UpdateProductSpec(ctx context.Context, specID int64, specName, specValue, sku string, price float64, stock int, weight, volume float64) error
	DeleteProductSpec(ctx context.Context, specID int64) error
	GetProductSpecs(ctx context.Context, productID int64) ([]*model.ProductSpec, error)
	GetProductSpec(ctx context.Context, specID int64) (*model.ProductSpec, error)
	UpdateSpecStatus(ctx context.Context, specID int64, status int8) error

	// 商品库存管理
	InitProductInventory(ctx context.Context, productID int64, specID *int64, totalStock int, minStock, maxStock int, warehouseID int64, location string) error
	UpdateStock(ctx context.Context, productID int64, specID *int64, quantity int, operation string) error
	CheckStock(ctx context.Context, productID int64, specID *int64, quantity int) (bool, error)
	GetProductInventory(ctx context.Context, productID int64, specID *int64) (*model.ProductInventory, error)
	GetLowStockProducts(ctx context.Context) ([]*model.ProductInventory, error)

	// 商品审核管理
	SubmitProductForAudit(ctx context.Context, productID int64) error
	AuditProduct(ctx context.Context, productID int64, auditorID int64, status int8, reason string) error
	GetPendingAudits(ctx context.Context, page, size int) ([]*model.ProductAudit, int64, error)
	GetProductAudit(ctx context.Context, productID int64) (*model.ProductAudit, error)

	// 商品详细信息
	GetProductWithDetails(ctx context.Context, productID int64) (*model.ProductWithDetails, error)

	// 商品搜索
	SearchProducts(ctx context.Context, query *model.ProductSearchQuery) ([]*model.Product, int64, error)

	// 批量操作
	BatchUpdateProductStatus(ctx context.Context, productIDs []int64, status int8) error
	BatchDeleteProducts(ctx context.Context, productIDs []int64) error
	ExportProducts(ctx context.Context, query *model.ProductSearchQuery) ([]byte, error)
}

// productEnhancedService 商品增强功能服务实现
type productEnhancedService struct {
	productEnhancedDAO dao.ProductEnhancedDAO
	productDAO         dao.ProductDAO
	uploadService      UploadService
}

// NewProductEnhancedService 创建商品增强功能服务实例
func NewProductEnhancedService() ProductEnhancedService {
	return &productEnhancedService{
		productEnhancedDAO: dao.NewProductEnhancedDAO(),
		productDAO:         dao.NewProductDAO(),
		uploadService:      NewUploadService(),
	}
}

// UploadProductImages 上传商品图片
func (s *productEnhancedService) UploadProductImages(ctx context.Context, productID int64, files []*multipart.FileHeader, imageType int8) ([]string, error) {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}

	// 上传图片到OSS
	folder := "products"
	urls, err := s.uploadService.UploadImages(ctx, files, folder)
	if err != nil {
		return nil, err
	}

	// 保存图片记录到数据库
	var savedURLs []string
	for i, url := range urls {
		if url != "" {
			image := &model.ProductImage{
				ProductID: productID,
				ImageURL:  url,
				ImageType: imageType,
				SortOrder: i,
				CreatedAt: time.Now(),
			}
			
			if err := s.productEnhancedDAO.CreateProductImage(ctx, image); err != nil {
				// 如果保存失败，尝试删除已上传的文件
				s.uploadService.DeleteFile(ctx, url)
				continue
			}
			savedURLs = append(savedURLs, url)
		}
	}

	return savedURLs, nil
}

// GetProductImages 获取商品图片
func (s *productEnhancedService) GetProductImages(ctx context.Context, productID int64, imageType int8) ([]*model.ProductImage, error) {
	return s.productEnhancedDAO.FindProductImages(ctx, productID, imageType)
}

// DeleteProductImage 删除商品图片
func (s *productEnhancedService) DeleteProductImage(ctx context.Context, imageID int64) error {
	// 查找图片记录
	images, err := s.productEnhancedDAO.FindProductImages(ctx, 0, -1)
	if err != nil {
		return err
	}

	var targetImage *model.ProductImage
	for _, img := range images {
		if img.ID == imageID {
			targetImage = img
			break
		}
	}

	if targetImage == nil {
		return ErrProductImageNotFound
	}

	// 删除OSS文件
	if err := s.uploadService.DeleteFile(ctx, targetImage.ImageURL); err != nil {
		// 记录日志，但不阻止删除数据库记录
	}

	// 删除数据库记录
	return s.productEnhancedDAO.DeleteProductImage(ctx, imageID)
}

// UpdateImageOrder 更新图片排序
func (s *productEnhancedService) UpdateImageOrder(ctx context.Context, imageID int64, sortOrder int) error {
	// 这里需要先查找图片，然后更新排序
	// 简化实现，实际应该查找后更新
	image := &model.ProductImage{
		ID:        imageID,
		SortOrder: sortOrder,
	}
	return s.productEnhancedDAO.UpdateProductImage(ctx, image)
}

// CreateProductSpec 创建商品规格
func (s *productEnhancedService) CreateProductSpec(ctx context.Context, productID int64, specName, specValue, sku string, price float64, stock int, weight, volume float64) (*model.ProductSpec, error) {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}

	spec := &model.ProductSpec{
		ProductID: productID,
		SpecName:  specName,
		SpecValue: specValue,
		SKU:       sku,
		Price:     price,
		Stock:     stock,
		Weight:    weight,
		Volume:    volume,
		Status:    1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.productEnhancedDAO.CreateProductSpec(ctx, spec); err != nil {
		return nil, err
	}

	// 初始化库存
	if stock > 0 {
		s.InitProductInventory(ctx, productID, &spec.ID, stock, 0, 0, 0, "")
	}

	return spec, nil
}

// UpdateProductSpec 更新商品规格
func (s *productEnhancedService) UpdateProductSpec(ctx context.Context, specID int64, specName, specValue, sku string, price float64, stock int, weight, volume float64) error {
	spec, err := s.productEnhancedDAO.FindProductSpecByID(ctx, specID)
	if err != nil {
		return err
	}
	if spec == nil {
		return ErrProductSpecNotFound
	}

	spec.SpecName = specName
	spec.SpecValue = specValue
	spec.SKU = sku
	spec.Price = price
	spec.Stock = stock
	spec.Weight = weight
	spec.Volume = volume
	spec.UpdatedAt = time.Now()

	return s.productEnhancedDAO.UpdateProductSpec(ctx, spec)
}

// DeleteProductSpec 删除商品规格
func (s *productEnhancedService) DeleteProductSpec(ctx context.Context, specID int64) error {
	spec, err := s.productEnhancedDAO.FindProductSpecByID(ctx, specID)
	if err != nil {
		return err
	}
	if spec == nil {
		return ErrProductSpecNotFound
	}

	return s.productEnhancedDAO.DeleteProductSpec(ctx, specID)
}

// GetProductSpecs 获取商品规格列表
func (s *productEnhancedService) GetProductSpecs(ctx context.Context, productID int64) ([]*model.ProductSpec, error) {
	return s.productEnhancedDAO.FindProductSpecs(ctx, productID)
}

// GetProductSpec 获取商品规格
func (s *productEnhancedService) GetProductSpec(ctx context.Context, specID int64) (*model.ProductSpec, error) {
	spec, err := s.productEnhancedDAO.FindProductSpecByID(ctx, specID)
	if err != nil {
		return nil, err
	}
	if spec == nil {
		return nil, ErrProductSpecNotFound
	}
	return spec, nil
}

// UpdateSpecStatus 更新规格状态
func (s *productEnhancedService) UpdateSpecStatus(ctx context.Context, specID int64, status int8) error {
	spec, err := s.productEnhancedDAO.FindProductSpecByID(ctx, specID)
	if err != nil {
		return err
	}
	if spec == nil {
		return ErrProductSpecNotFound
	}

	spec.Status = status
	spec.UpdatedAt = time.Now()

	return s.productEnhancedDAO.UpdateProductSpec(ctx, spec)
}

// InitProductInventory 初始化商品库存
func (s *productEnhancedService) InitProductInventory(ctx context.Context, productID int64, specID *int64, totalStock int, minStock, maxStock int, warehouseID int64, location string) error {
	inventory := &model.ProductInventory{
		ProductID:      productID,
		SpecID:         specID,
		TotalStock:     totalStock,
		AvailableStock: totalStock,
		ReservedStock:  0,
		SoldStock:      0,
		MinStock:       minStock,
		MaxStock:       maxStock,
		WarehouseID:    warehouseID,
		Location:       location,
		UpdatedAt:      time.Now(),
	}

	return s.productEnhancedDAO.CreateProductInventory(ctx, inventory)
}

// UpdateStock 更新库存
func (s *productEnhancedService) UpdateStock(ctx context.Context, productID int64, specID *int64, quantity int, operation string) error {
	return s.productEnhancedDAO.UpdateStock(ctx, productID, specID, quantity, operation)
}

// CheckStock 检查库存
func (s *productEnhancedService) CheckStock(ctx context.Context, productID int64, specID *int64, quantity int) (bool, error) {
	return s.productEnhancedDAO.CheckStock(ctx, productID, specID, quantity)
}

// GetProductInventory 获取商品库存
func (s *productEnhancedService) GetProductInventory(ctx context.Context, productID int64, specID *int64) (*model.ProductInventory, error) {
	return s.productEnhancedDAO.FindProductInventory(ctx, productID, specID)
}

// GetLowStockProducts 获取低库存商品
func (s *productEnhancedService) GetLowStockProducts(ctx context.Context) ([]*model.ProductInventory, error) {
	// 这里需要在DAO中实现查询低库存的方法
	// 简化实现，返回空列表
	return []*model.ProductInventory{}, nil
}

// SubmitProductForAudit 提交商品审核
func (s *productEnhancedService) SubmitProductForAudit(ctx context.Context, productID int64) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 检查是否已有待审核记录
	existAudit, err := s.productEnhancedDAO.FindProductAudit(ctx, productID)
	if err != nil {
		return err
	}
	if existAudit != nil && existAudit.Status == 0 {
		return errors.New("商品已在审核中")
	}

	audit := &model.ProductAudit{
		ProductID: productID,
		Status:    0, // 待审核
		CreatedAt: time.Now(),
	}

	return s.productEnhancedDAO.CreateProductAudit(ctx, audit)
}

// AuditProduct 审核商品
func (s *productEnhancedService) AuditProduct(ctx context.Context, productID int64, auditorID int64, status int8, reason string) error {
	audit, err := s.productEnhancedDAO.FindProductAudit(ctx, productID)
	if err != nil {
		return err
	}
	if audit == nil {
		return ErrProductAuditNotFound
	}

	now := time.Now()
	audit.AuditorID = auditorID
	audit.Status = status
	audit.Reason = reason
	audit.AuditTime = &now

	return s.productEnhancedDAO.UpdateProductAudit(ctx, audit)
}

// GetPendingAudits 获取待审核商品列表
func (s *productEnhancedService) GetPendingAudits(ctx context.Context, page, size int) ([]*model.ProductAudit, int64, error) {
	offset := (page - 1) * size
	audits, err := s.productEnhancedDAO.ListPendingAudits(ctx, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.productEnhancedDAO.CountPendingAudits(ctx)
	if err != nil {
		return nil, 0, err
	}

	return audits, total, nil
}

// GetProductAudit 获取商品审核信息
func (s *productEnhancedService) GetProductAudit(ctx context.Context, productID int64) (*model.ProductAudit, error) {
	return s.productEnhancedDAO.FindProductAudit(ctx, productID)
}

// GetProductWithDetails 获取商品详细信息
func (s *productEnhancedService) GetProductWithDetails(ctx context.Context, productID int64) (*model.ProductWithDetails, error) {
	return s.productEnhancedDAO.FindProductWithDetails(ctx, productID)
}

// SearchProducts 搜索商品
func (s *productEnhancedService) SearchProducts(ctx context.Context, query *model.ProductSearchQuery) ([]*model.Product, int64, error) {
	return s.productEnhancedDAO.SearchProducts(ctx, query)
}

// BatchUpdateProductStatus 批量更新商品状态
func (s *productEnhancedService) BatchUpdateProductStatus(ctx context.Context, productIDs []int64, status int8) error {
	for _, productID := range productIDs {
		if err := s.productDAO.UpdateStatus(ctx, productID, status); err != nil {
			return err
		}
	}
	return nil
}

// BatchDeleteProducts 批量删除商品
func (s *productEnhancedService) BatchDeleteProducts(ctx context.Context, productIDs []int64) error {
	for _, productID := range productIDs {
		// 删除商品图片
		s.productEnhancedDAO.DeleteProductImages(ctx, productID)
		// 删除商品规格
		s.productEnhancedDAO.DeleteProductSpecs(ctx, productID)
		// 删除商品
		if err := s.productDAO.Delete(ctx, productID); err != nil {
			return err
		}
	}
	return nil
}

// ExportProducts 导出商品
func (s *productEnhancedService) ExportProducts(ctx context.Context, query *model.ProductSearchQuery) ([]byte, error) {
	// 这里应该实现Excel导出功能
	// 简化实现，返回空数据
	return []byte{}, nil
}
