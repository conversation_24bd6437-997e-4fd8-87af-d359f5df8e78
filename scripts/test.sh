#!/bin/bash

# 昆明花卉拍卖系统测试脚本

set -e

echo "🌸 昆明花卉拍卖系统 - 运行测试"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Go环境
check_go() {
    print_message $BLUE "检查Go环境..."
    if ! command -v go &> /dev/null; then
        print_message $RED "错误: Go未安装或不在PATH中"
        exit 1
    fi
    print_message $GREEN "✓ Go环境正常: $(go version)"
}

# 下载依赖
download_deps() {
    print_message $BLUE "下载依赖..."
    go mod download
    go mod tidy
    print_message $GREEN "✓ 依赖下载完成"
}

# 运行代码格式化
format_code() {
    print_message $BLUE "格式化代码..."
    go fmt ./...
    print_message $GREEN "✓ 代码格式化完成"
}

# 运行代码检查
lint_code() {
    print_message $BLUE "运行代码检查..."
    if command -v golangci-lint &> /dev/null; then
        golangci-lint run
        print_message $GREEN "✓ 代码检查通过"
    else
        print_message $YELLOW "⚠ golangci-lint未安装，跳过代码检查"
    fi
}

# 运行单元测试
run_unit_tests() {
    print_message $BLUE "运行单元测试..."
    
    # 设置测试环境变量
    export GIN_MODE=test
    export CONFIG_FILE=configs/config.test.yaml
    
    # 创建测试日志目录
    mkdir -p logs
    
    # 运行测试并生成覆盖率报告
    go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 单元测试通过"
        
        # 生成覆盖率报告
        if [ -f coverage.out ]; then
            go tool cover -html=coverage.out -o coverage.html
            coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
            print_message $GREEN "✓ 测试覆盖率: $coverage"
            print_message $BLUE "覆盖率报告已生成: coverage.html"
        fi
    else
        print_message $RED "✗ 单元测试失败"
        exit 1
    fi
}

# 运行基准测试
run_benchmark_tests() {
    print_message $BLUE "运行基准测试..."
    go test -bench=. -benchmem ./... > benchmark.txt
    print_message $GREEN "✓ 基准测试完成，结果保存到 benchmark.txt"
}

# 运行集成测试
run_integration_tests() {
    print_message $BLUE "运行集成测试..."
    # 这里可以添加集成测试逻辑
    print_message $YELLOW "⚠ 集成测试暂未实现"
}

# 清理测试文件
cleanup() {
    print_message $BLUE "清理测试文件..."
    rm -f coverage.out
    print_message $GREEN "✓ 清理完成"
}

# 主函数
main() {
    print_message $BLUE "开始测试流程..."
    
    # 检查参数
    case "${1:-all}" in
        "unit")
            check_go
            download_deps
            run_unit_tests
            ;;
        "bench")
            check_go
            download_deps
            run_benchmark_tests
            ;;
        "lint")
            check_go
            format_code
            lint_code
            ;;
        "integration")
            check_go
            download_deps
            run_integration_tests
            ;;
        "all")
            check_go
            download_deps
            format_code
            lint_code
            run_unit_tests
            run_benchmark_tests
            ;;
        "clean")
            cleanup
            ;;
        *)
            echo "用法: $0 [unit|bench|lint|integration|all|clean]"
            echo "  unit        - 运行单元测试"
            echo "  bench       - 运行基准测试"
            echo "  lint        - 运行代码检查"
            echo "  integration - 运行集成测试"
            echo "  all         - 运行所有测试 (默认)"
            echo "  clean       - 清理测试文件"
            exit 1
            ;;
    esac
    
    print_message $GREEN "🎉 测试流程完成!"
}

# 执行主函数
main "$@"
