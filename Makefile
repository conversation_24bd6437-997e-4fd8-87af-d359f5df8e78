# 昆明花卉拍卖系统 Makefile

.PHONY: help build run test clean docker-build docker-run docker-stop deps fmt lint

# 默认目标
help:
	@echo "昆明花卉拍卖系统 - 可用命令:"
	@echo "  build        - 构建应用程序"
	@echo "  run          - 运行应用程序"
	@echo "  test         - 运行测试"
	@echo "  clean        - 清理构建文件"
	@echo "  deps         - 下载依赖"
	@echo "  fmt          - 格式化代码"
	@echo "  lint         - 代码检查"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo "  docker-stop  - 停止Docker容器"
	@echo "  dev          - 启动开发环境"
	@echo "  prod         - 启动生产环境"

# 应用程序名称
APP_NAME=flower-auction
VERSION=1.0.0
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD)

# Go相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 构建标志
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 构建应用程序
build:
	@echo "构建应用程序..."
	$(GOBUILD) $(LDFLAGS) -o bin/$(APP_NAME) .
	@echo "构建完成: bin/$(APP_NAME)"

# 运行应用程序
run:
	@echo "运行应用程序..."
	$(GOCMD) run main.go

# 运行测试
test:
	@echo "运行测试..."
	$(GOTEST) -v ./...

# 清理构建文件
clean:
	@echo "清理构建文件..."
	$(GOCLEAN)
	rm -rf bin/
	rm -rf logs/

# 下载依赖
deps:
	@echo "下载依赖..."
	$(GOMOD) download
	$(GOMOD) tidy

# 格式化代码
fmt:
	@echo "格式化代码..."
	$(GOCMD) fmt ./...

# 代码检查
lint:
	@echo "代码检查..."
	golangci-lint run

# 构建Docker镜像
docker-build:
	@echo "构建Docker镜像..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# 运行Docker容器
docker-run:
	@echo "运行Docker容器..."
	docker-compose up -d

# 停止Docker容器
docker-stop:
	@echo "停止Docker容器..."
	docker-compose down

# 启动开发环境
dev:
	@echo "启动开发环境..."
	docker-compose -f docker-compose.yml up -d mysql redis rabbitmq
	@echo "等待数据库启动..."
	sleep 10
	@echo "运行应用程序..."
	$(GOCMD) run main.go

# 启动生产环境
prod:
	@echo "启动生产环境..."
	docker-compose up -d

# 查看日志
logs:
	@echo "查看应用日志..."
	docker-compose logs -f app

# 数据库迁移
migrate:
	@echo "执行数据库迁移..."
	# 这里可以添加数据库迁移命令

# 生成API文档
docs:
	@echo "生成API文档..."
	swag init

# 安装开发工具
install-tools:
	@echo "安装开发工具..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest

# 检查代码质量
quality: fmt lint test
	@echo "代码质量检查完成"

# 完整构建流程
all: clean deps quality build
	@echo "完整构建流程完成"
