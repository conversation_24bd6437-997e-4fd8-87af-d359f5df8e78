package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/service"
)

// QualityHandler 质检相关接口处理器
type QualityHandler struct {
	qualityService service.QualityService
}

// NewQualityHandler 创建质检接口处理器
func NewQualityHandler() *QualityHandler {
	return &QualityHandler{
		qualityService: service.NewQualityService(),
	}
}

// RegisterRoutes 注册路由
func (h *QualityHandler) RegisterRoutes(r *gin.Engine) {
	// 批次管理
	batch := r.Group("/api/v1/batches")
	{
		batch.POST("", h.CreateBatch)
		batch.PUT("/:id", h.UpdateBatch)
		batch.GET("/:id", h.GetBatch)
		batch.GET("/no/:batchNo", h.GetBatchByNo)
		batch.GET("", h.ListBatches)
	}

	// 质检管理
	quality := r.Group("/api/v1/quality")
	{
		quality.POST("/check/:batchId", h.StartQualityCheck)
		quality.PUT("/check/:id", h.UpdateQualityCheck)
		quality.POST("/check/:id/defect", h.AddDefect)
		quality.PUT("/check/:id/finish", h.FinishQualityCheck)
		quality.GET("/check/:id", h.GetQualityCheck)
		quality.GET("/checks", h.ListQualityChecks)
	}

	// 质检标准管理
	standard := r.Group("/api/v1/quality/standards")
	{
		standard.POST("", h.CreateQualityStandard)
		standard.PUT("/:id", h.UpdateQualityStandard)
		standard.GET("/category/:categoryId", h.ListQualityStandards)
		standard.GET("/category/:categoryId/level/:level", h.GetQualityStandard)
	}
}

// CreateBatchRequest 创建批次请求
type CreateBatchRequest struct {
	BatchNo    string `json:"batchNo" binding:"required"`
	ProductID  int64  `json:"productId" binding:"required"`
	SupplierID int64  `json:"supplierId" binding:"required"`
	Quantity   int    `json:"quantity" binding:"required,min=1"`
	Unit       string `json:"unit" binding:"required"`
}

// CreateBatch 创建批次
func (h *QualityHandler) CreateBatch(c *gin.Context) {
	var req CreateBatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	batch, err := h.qualityService.CreateBatch(c.Request.Context(), req.BatchNo, req.ProductID, req.SupplierID, req.Quantity, req.Unit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, batch)
}

// UpdateBatchRequest 更新批次请求
type UpdateBatchRequest struct {
	Quantity     int  `json:"quantity" binding:"required,min=1"`
	QualityLevel int8 `json:"qualityLevel" binding:"required,min=1,max=5"`
	Status       int8 `json:"status" binding:"required,min=0,max=7"`
}

// UpdateBatch 更新批次
func (h *QualityHandler) UpdateBatch(c *gin.Context) {
	batchID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateBatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.qualityService.UpdateBatch(c.Request.Context(), batchID, req.Quantity, req.QualityLevel, req.Status); err != nil {
		if err == service.ErrBatchNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "批次不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "批次更新成功"})
}

// GetBatch 获取批次信息
func (h *QualityHandler) GetBatch(c *gin.Context) {
	batchID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	batch, err := h.qualityService.GetBatch(c.Request.Context(), batchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if batch == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "批次不存在"})
		return
	}

	c.JSON(http.StatusOK, batch)
}

// GetBatchByNo 根据批次号获取批次信息
func (h *QualityHandler) GetBatchByNo(c *gin.Context) {
	batchNo := c.Param("batchNo")
	if batchNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "批次号不能为空"})
		return
	}

	batch, err := h.qualityService.GetBatchByNo(c.Request.Context(), batchNo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if batch == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "批次不存在"})
		return
	}

	c.JSON(http.StatusOK, batch)
}

// ListBatches 查询批次列表
func (h *QualityHandler) ListBatches(c *gin.Context) {
	page, size := ParsePagination(c)
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))

	batches, total, err := h.qualityService.ListBatches(c.Request.Context(), int8(status), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  batches,
		Total: total,
		Page:  page,
		Size:  size,
	})
}

// StartQualityCheckRequest 开始质检请求
type StartQualityCheckRequest struct {
	InspectorID int64 `json:"inspectorId" binding:"required"`
}

// StartQualityCheck 开始质检
func (h *QualityHandler) StartQualityCheck(c *gin.Context) {
	batchID, err := ParseParamID(c, "batchId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req StartQualityCheckRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	check, err := h.qualityService.StartQualityCheck(c.Request.Context(), batchID, req.InspectorID)
	if err != nil {
		if err == service.ErrBatchNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "批次不存在"})
			return
		}
		if err == service.ErrBatchAlreadyChecked {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "批次已质检"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, check)
}

// UpdateQualityCheckRequest 更新质检记录请求
type UpdateQualityCheckRequest struct {
	ActualQty    int     `json:"actualQty" binding:"required,min=0"`
	QualityLevel int8    `json:"qualityLevel" binding:"required,min=1,max=5"`
	DefectRate   float64 `json:"defectRate" binding:"required,min=0,max=100"`
	Remarks      string  `json:"remarks"`
}

// UpdateQualityCheck 更新质检记录
func (h *QualityHandler) UpdateQualityCheck(c *gin.Context) {
	checkID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateQualityCheckRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.qualityService.UpdateQualityCheck(c.Request.Context(), checkID, req.ActualQty, req.QualityLevel, req.DefectRate, req.Remarks); err != nil {
		if err == service.ErrQualityCheckNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "质检记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "质检记录更新成功"})
}

// AddDefectRequest 添加瑕疵记录请求
type AddDefectRequest struct {
	DefectType  string `json:"defectType" binding:"required"`
	DefectCode  string `json:"defectCode" binding:"required"`
	Severity    int8   `json:"severity" binding:"required,min=1,max=3"`
	Description string `json:"description"`
	ImageURL    string `json:"imageUrl"`
}

// AddDefect 添加瑕疵记录
func (h *QualityHandler) AddDefect(c *gin.Context) {
	checkID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req AddDefectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.qualityService.AddDefect(c.Request.Context(), checkID, req.DefectType, req.DefectCode, req.Severity, req.Description, req.ImageURL); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "瑕疵记录添加成功"})
}

// FinishQualityCheck 完成质检
func (h *QualityHandler) FinishQualityCheck(c *gin.Context) {
	checkID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.qualityService.FinishQualityCheck(c.Request.Context(), checkID); err != nil {
		if err == service.ErrQualityCheckNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "质检记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "质检完成"})
}

// GetQualityCheck 获取质检详情
func (h *QualityHandler) GetQualityCheck(c *gin.Context) {
	checkID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	check, err := h.qualityService.GetQualityCheck(c.Request.Context(), checkID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if check == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "质检记录不存在"})
		return
	}

	c.JSON(http.StatusOK, check)
}

// ListQualityChecks 查询质检记录列表
func (h *QualityHandler) ListQualityChecks(c *gin.Context) {
	page, size := ParsePagination(c)
	status, _ := strconv.Atoi(c.DefaultQuery("status", "-1"))

	checks, total, err := h.qualityService.ListQualityChecks(c.Request.Context(), int8(status), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  checks,
		Total: total,
		Page:  page,
		Size:  size,
	})
}

// CreateQualityStandardRequest 创建质检标准请求
type CreateQualityStandardRequest struct {
	CategoryID   int64   `json:"categoryId" binding:"required"`
	StandardName string  `json:"standardName" binding:"required"`
	Level        int8    `json:"level" binding:"required,min=1,max=5"`
	DefectRate   float64 `json:"defectRate" binding:"required,min=0,max=100"`
	Description  string  `json:"description"`
}

// CreateQualityStandard 创建质检标准
func (h *QualityHandler) CreateQualityStandard(c *gin.Context) {
	var req CreateQualityStandardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	standard, err := h.qualityService.CreateQualityStandard(c.Request.Context(), req.CategoryID, req.StandardName, req.Level, req.DefectRate, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, standard)
}

// UpdateQualityStandardRequest 更新质检标准请求
type UpdateQualityStandardRequest struct {
	StandardName string  `json:"standardName" binding:"required"`
	DefectRate   float64 `json:"defectRate" binding:"required,min=0,max=100"`
	Description  string  `json:"description"`
}

// UpdateQualityStandard 更新质检标准
func (h *QualityHandler) UpdateQualityStandard(c *gin.Context) {
	standardID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateQualityStandardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.qualityService.UpdateQualityStandard(c.Request.Context(), standardID, req.StandardName, req.DefectRate, req.Description); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "质检标准更新成功"})
}

// GetQualityStandard 获取质检标准
func (h *QualityHandler) GetQualityStandard(c *gin.Context) {
	categoryID, err := ParseParamID(c, "categoryId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	level, err := strconv.Atoi(c.Param("level"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "等级参数错误"})
		return
	}

	standard, err := h.qualityService.GetQualityStandard(c.Request.Context(), categoryID, int8(level))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}
	if standard == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "质检标准不存在"})
		return
	}

	c.JSON(http.StatusOK, standard)
}

// ListQualityStandards 查询质检标准列表
func (h *QualityHandler) ListQualityStandards(c *gin.Context) {
	categoryID, err := ParseParamID(c, "categoryId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	standards, err := h.qualityService.ListQualityStandards(c.Request.Context(), categoryID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, standards)
}
