package model

import (
	"time"
)

// Product 商品模型
type Product struct {
	ID           int64     `gorm:"primaryKey;column:id" json:"id"`
	Name         string    `gorm:"column:name" json:"name"`
	CategoryID   int64     `gorm:"column:category_id" json:"categoryId"`
	Description  string    `gorm:"column:description" json:"description"`
	QualityLevel int8      `gorm:"column:quality_level" json:"qualityLevel"` // 1-优 2-良 3-中
	Origin       string    `gorm:"column:origin" json:"origin"`
	SupplierID   int64     `gorm:"column:supplier_id" json:"supplierId"`
	Status       int8      `gorm:"column:status" json:"status"` // 0-下架 1-上架
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "product"
}

// Category 商品类别模型
type Category struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	Name      string    `gorm:"column:name" json:"name"`
	ParentID  *int64    `gorm:"column:parent_id" json:"parentId"`
	Level     int8      `gorm:"column:level" json:"level"`
	SortOrder int       `gorm:"column:sort_order" json:"sortOrder"`
	Status    int8      `gorm:"column:status" json:"status"` // 0-禁用 1-启用
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Category) TableName() string {
	return "category"
}

// CategoryTree 商品类别树形结构
type CategoryTree struct {
	Category
	Children []CategoryTree `json:"children"`
}

// ProductWithCategory 商品信息（包含类别）
type ProductWithCategory struct {
	Product
	Category Category `json:"category"`
}

// ProductImage 商品图片模型
type ProductImage struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	ProductID int64     `gorm:"column:product_id;not null" json:"productId"`        // 商品ID
	ImageURL  string    `gorm:"column:image_url;size:500;not null" json:"imageUrl"` // 图片URL
	ImageType int8      `gorm:"column:image_type;default:0" json:"imageType"`       // 图片类型 0-主图 1-详情图
	SortOrder int       `gorm:"column:sort_order;default:0" json:"sortOrder"`       // 排序
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (ProductImage) TableName() string {
	return "product_image"
}

// ProductSpec 商品规格模型
type ProductSpec struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"`
	ProductID int64     `gorm:"column:product_id;not null" json:"productId"`          // 商品ID
	SpecName  string    `gorm:"column:spec_name;size:50;not null" json:"specName"`    // 规格名称
	SpecValue string    `gorm:"column:spec_value;size:100;not null" json:"specValue"` // 规格值
	Price     float64   `gorm:"column:price;type:decimal(10,2)" json:"price"`         // 价格
	Stock     int       `gorm:"column:stock;default:0" json:"stock"`                  // 库存
	SKU       string    `gorm:"column:sku;size:100" json:"sku"`                       // SKU编码
	Weight    float64   `gorm:"column:weight;type:decimal(8,3)" json:"weight"`        // 重量(kg)
	Volume    float64   `gorm:"column:volume;type:decimal(8,3)" json:"volume"`        // 体积(m³)
	Status    int8      `gorm:"column:status;default:1" json:"status"`                // 状态 0-禁用 1-启用
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (ProductSpec) TableName() string {
	return "product_spec"
}

// ProductInventory 商品库存模型
type ProductInventory struct {
	ID             int64     `gorm:"primaryKey;column:id" json:"id"`
	ProductID      int64     `gorm:"column:product_id;not null" json:"productId"`            // 商品ID
	SpecID         *int64    `gorm:"column:spec_id" json:"specId"`                           // 规格ID（可选）
	TotalStock     int       `gorm:"column:total_stock;default:0" json:"totalStock"`         // 总库存
	AvailableStock int       `gorm:"column:available_stock;default:0" json:"availableStock"` // 可用库存
	ReservedStock  int       `gorm:"column:reserved_stock;default:0" json:"reservedStock"`   // 预留库存
	SoldStock      int       `gorm:"column:sold_stock;default:0" json:"soldStock"`           // 已售库存
	MinStock       int       `gorm:"column:min_stock;default:0" json:"minStock"`             // 最小库存预警
	MaxStock       int       `gorm:"column:max_stock;default:0" json:"maxStock"`             // 最大库存
	WarehouseID    int64     `gorm:"column:warehouse_id" json:"warehouseId"`                 // 仓库ID
	Location       string    `gorm:"column:location;size:100" json:"location"`               // 存储位置
	UpdatedAt      time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (ProductInventory) TableName() string {
	return "product_inventory"
}

// ProductAudit 商品审核模型
type ProductAudit struct {
	ID        int64      `gorm:"primaryKey;column:id" json:"id"`
	ProductID int64      `gorm:"column:product_id;not null" json:"productId"` // 商品ID
	AuditorID int64      `gorm:"column:auditor_id" json:"auditorId"`          // 审核员ID
	Status    int8       `gorm:"column:status;default:0" json:"status"`       // 审核状态 0-待审核 1-通过 2-拒绝
	Reason    string     `gorm:"column:reason;size:500" json:"reason"`        // 审核意见
	AuditTime *time.Time `gorm:"column:audit_time" json:"auditTime"`          // 审核时间
	CreatedAt time.Time  `gorm:"column:created_at" json:"createdAt"`
}

// TableName 指定表名
func (ProductAudit) TableName() string {
	return "product_audit"
}

// ProductWithDetails 商品详细信息（包含图片、规格、库存等）
type ProductWithDetails struct {
	Product
	Category  *Category         `json:"category,omitempty"`
	Images    []ProductImage    `json:"images,omitempty"`
	Specs     []ProductSpec     `json:"specs,omitempty"`
	Inventory *ProductInventory `json:"inventory,omitempty"`
	Audit     *ProductAudit     `json:"audit,omitempty"`
}

// ProductSearchQuery 商品搜索查询条件
type ProductSearchQuery struct {
	Keyword    string    `json:"keyword"`
	CategoryID int64     `json:"categoryId"`
	MinPrice   float64   `json:"minPrice"`
	MaxPrice   float64   `json:"maxPrice"`
	Status     int8      `json:"status"`
	SupplierID int64     `json:"supplierId"`
	StartTime  time.Time `json:"startTime"`
	EndTime    time.Time `json:"endTime"`
	SortBy     string    `json:"sortBy"`    // 排序字段
	SortOrder  string    `json:"sortOrder"` // 排序方向 asc/desc
	Page       int       `json:"page"`
	Size       int       `json:"size"`
}
